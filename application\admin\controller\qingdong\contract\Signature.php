<?php

namespace app\admin\controller\qingdong\contract;

use app\admin\controller\qingdong\Base;
use addons\qingdong\model\Contract;
use addons\qingdong\model\ContractSignature;
use addons\qingdong\model\ContractShare;
use think\Exception;

/**
 * 电子签名管理
 */
class Signature extends Base
{
    /**
     * 发起签名流程
     */
    public function initSign()
    {
        $contractId = input('contract_id');
        
        if (!$contractId) {
            $this->error('合同ID不能为空');
        }
        
        try {
            ContractSignature::initSignProcess($contractId);
            $this->success('签名流程已发起');
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * 签名页面
     */
    public function sign()
    {
        $contractId = input('contract_id');
        $signerType = input('signer_type', 'customer');
        $signerId = input('signer_id');
        
        if (!$contractId) {
            $this->error('参数错误');
        }
        
        // 获取合同信息
        $contract = Contract::with(['customer'])->find($contractId);
        if (!$contract) {
            $this->error('合同不存在');
        }
        
        // 获取签名状态
        $signatures = ContractSignature::getContractSignStatus($contractId);
        
        // 检查当前用户是否可以签名
        $canSign = false;
        $currentSignature = null;
        
        foreach ($signatures as $sig) {
            if ($sig['signer_type'] == $signerType && 
                $sig['signer_id'] == $signerId && 
                $sig['status'] == ContractSignature::STATUS_UNSIGNED) {
                
                // 检查是否轮到当前用户签名
                if (ContractSignature::checkSignOrder($contractId, $sig['order_seq'])) {
                    $canSign = true;
                    $currentSignature = $sig;
                    break;
                }
            }
        }
        
        $this->assign('contract', $contract);
        $this->assign('signatures', $signatures);
        $this->assign('can_sign', $canSign);
        $this->assign('current_signature', $currentSignature);
        $this->assign('signer_type', $signerType);
        $this->assign('signer_id', $signerId);
        
        return $this->view->fetch();
    }

    /**
     * 处理签名提交
     */
    public function processSign()
    {
        if (!$this->request->isPost()) {
            $this->error('请求方法错误');
        }
        
        $params = $this->request->post();
        
        try {
            // 验证必要参数
            if (empty($params['contract_id']) || 
                empty($params['signer_type']) || 
                !isset($params['signer_id'])) {
                $this->error('参数不完整');
            }
            
            // 处理签名图片上传
            if (isset($_FILES['signature_image']) && $_FILES['signature_image']['error'] == 0) {
                $uploadPath = ROOT_PATH . 'public/uploads/signatures/';
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }
                
                $fileName = md5(uniqid() . microtime()) . '.png';
                $filePath = $uploadPath . $fileName;
                
                if (move_uploaded_file($_FILES['signature_image']['tmp_name'], $filePath)) {
                    $params['signature_image'] = '/uploads/signatures/' . $fileName;
                }
            }
            
            $result = ContractSignature::processSign($params);
            
            $this->success('签名成功', '', $result);
            
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * 创建分享链接
     */
    public function createShare()
    {
        if (!$this->request->isPost()) {
            $this->error('请求方法错误');
        }
        
        $contractId = input('contract_id');
        $expireDays = input('expire_days', 30);
        $password = input('password', '');
        
        if (!$contractId) {
            $this->error('合同ID不能为空');
        }
        
        try {
            $options = [
                'expire_days' => $expireDays,
                'password' => $password
            ];
            
            $result = ContractShare::createShare($contractId, $options);
            
            $this->success('分享链接创建成功', '', $result);
            
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * 获取合同签名状态
     */
    public function getSignStatus()
    {
        $contractId = input('contract_id');
        
        if (!$contractId) {
            $this->error('合同ID不能为空');
        }
        
        try {
            $signatures = ContractSignature::getContractSignStatus($contractId);
            
            $this->success('获取成功', '', [
                'signatures' => $signatures
            ]);
            
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * 管理分享链接
     */
    public function manageShares()
    {
        $contractId = input('contract_id');
        
        if (!$contractId) {
            $this->error('合同ID不能为空');
        }
        
        if ($this->request->isAjax()) {
            try {
                $shares = ContractShare::getContractShares($contractId);
                
                $result = [];
                foreach ($shares as $share) {
                    $result[] = $share->getShareInfo();
                }
                
                $this->success('获取成功', '', $result);
                
            } catch (Exception $e) {
                $this->error($e->getMessage());
            }
        }
        
        $contract = Contract::find($contractId);
        if (!$contract) {
            $this->error('合同不存在');
        }
        
        $this->assign('contract', $contract);
        return $this->view->fetch();
    }

    /**
     * 撤销分享
     */
    public function revokeShare()
    {
        if (!$this->request->isPost()) {
            $this->error('请求方法错误');
        }
        
        $shareId = input('share_id');
        
        if (!$shareId) {
            $this->error('分享ID不能为空');
        }
        
        try {
            ContractShare::revokeShare($shareId, $this->_staff->id);
            $this->success('分享已撤销');
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
    }
} 