{foreach name="form_data" item="val"}
    {if condition="$val.id eq 'end_time'"}
        {php}continue;{/php}
    {/if}

{if condition="$val.config.is_delete == true || true"}

{if condition="isset($val.config.addShow) && $val.config.addShow"}
{php}continue;{/php}
{/if}
{eq name="val.component" value="input"}
<div class="col-xs-12 col-sm-6">
	<div class="form-group col-sm-10">
		<label for="c-{$val.id}">{$val.config.label}:</label>
		{if condition="$val.id eq 'name' && $val.config.label eq '合同名称' "}
		<select id="c-{$val.id}" class="form-control" {if condition="$val.config.required == true"}data-rule="required"{/if} name="row[{$val.id}]">
			<option value="">请选择</option>
			<option value="签证" {if condition="isset($row[$val['id']]) && $row[$val['id']] eq '签证'"}selected{/if}>签证</option>
			<option value="简历" {if condition="isset($row[$val['id']]) && $row[$val['id']] eq '简历'"}selected{/if}>简历</option>
			<option value="咨询费" {if condition="isset($row[$val['id']]) && $row[$val['id']] eq '咨询费'"}selected{/if}>咨询</option>

		</select>
		{elseif condition="$val.id eq 'mobile' || $val.id eq 'source'"/}
		<!-- 手机号码和客户来源字段设为只读 -->
		<input id="c-{$val.id}" class="form-control" placeholder="{$val.config.placeholder}" name="row[{$val.id}]"
		type="text" {if condition="isset($row[$val['id']])"}value="{:$row[$val['id']]}"{/if} readonly style="background-color: #f5f5f5; cursor: not-allowed;">
		<small class="text-muted">此字段不允许修改</small>
		{else/}
		<input id="c-{$val.id}" class="form-control"{if condition="$val.config.required == true"}data-rule="required"{/if} placeholder="{$val.config.placeholder}" name="row[{$val.id}]"
		type="text" {if condition="isset($row[$val['id']])"}value="{:$row[$val['id']]}"{/if} >
		{/if}
	</div>
</div>
{/eq}

{eq name="val.component" value="input-number"}
<div class="col-xs-12 col-sm-6">
	<div class="form-group col-sm-10">
		<label for="c-{$val.id}">{$val.config.label}:</label>
		<input id="c-{$val.id}" class="form-control" {if condition="$val.config.required == true"}data-rule="required"{/if}placeholder="{$val.config.placeholder}" name="row[{$val.id}]"
		type="number" {if condition="isset($row[$val['id']])"}value="{:$row[$val['id']]}"{/if}>
	</div>
</div>
{/eq}
{eq name="val.component" value="textarea"}
<div class="col-xs-12 col-sm-10">
	<div class="form-group col-sm-10">
		<label for="c-{$val.id}">{$val.config.label}:</label>
		<textarea id="c-{$val.id}" class="form-control" {if condition="$val.config.required == true"}data-rule="required"{/if}rows="3" name="row[{$val.id}]"
		placeholder="{$val.config.placeholder}">{if condition="isset($row[$val['id']])"}{:$row[$val['id']]}{/if}</textarea>
	</div>
</div>
{/eq}

{eq name="val.component" value="radio"}
<div class="col-xs-12 col-sm-6">
	<div class="form-group col-sm-10">
		<label >{$val.config.label}:</label>
		<div class="radio">
			{foreach name="val.config.content" item="vo"}
			<label><input name="row[{$val.id}]" type="radio" value="{$vo.value}"
						  {if condition="isset($row[$val['id']])"}
						  {eq name="$row[$val['id']]" value="$vo['value']" }checked{/eq}
				{/if} /> {$vo.value}</label>
			{/foreach}
		</div>

	</div>
</div>
{/eq}

{eq name="val.component" value="checkbox"}
<div class="col-xs-12 col-sm-6">
	<div class="form-group col-sm-10">
		<label >{$val.config.label}:</label>
		<div class="checkbox">
			{foreach name="val.config.content" item="vo"}

			<label class="checkbox-inline"><input name="row[{$val.id}][]" type="checkbox" {if condition="isset($row[$val['id']])"} {in name="$vo['value']" value="$row[$val['id']]" }checked{/in}{/if}  value="{$vo.value}"/>{$vo.value}</label>

			{/foreach}
		</div>

	</div>
</div>
{/eq}
{eq name="val.component" value="Cascader"}
<div class="col-xs-12 col-sm-6">
	<div class="form-group col-sm-10">
		<label for="c-{$val.id}">{$val.config.label}:</label>
		<div class='control-relative'>
			<input id="c-{$val.id}" class="form-control"{if condition="$val.config.required == true"}data-rule="required"{/if} data-toggle="city-picker" name="row[{$val.id}]" type="text"
			{if condition="isset($row[$val['id']])"} value="{:$row[$val['id']]}"{/if}/>
		</div>
	</div>
</div>
{/eq}

{eq name="val.component" value="select"}
<div class="col-xs-12 col-sm-6">
	<div class="form-group col-sm-10">
		<label for="c-{$val.id}">{$val.config.label}:</label>
		{if condition="$val.id eq 'source'"}
		<!-- 客户来源字段设为只读 -->
		<input id="c-{$val.id}" class="form-control" name="row[{$val.id}]" type="text"
		{if condition="isset($row[$val['id']])"}value="{:$row[$val['id']]}"{/if} readonly style="background-color: #f5f5f5; cursor: not-allowed;">
		<small class="text-muted">此字段不允许修改</small>
		{else/}
		<select id="c-{$val.id}"  class="form-control selectpicker" {if condition="isset($val.config.multiple) && $val.config.multiple == true"} name="row[{$val.id}][]" multiple="" {else\} name="row[{$val.id}]" {/if} data-live-search='true'>
			<option value="">请选择</option>
			{foreach name="val.config.content" item="vo" k="key"}
				{if condition="isset($row[$val['id']])"}
					{if condition="$val.data_value neq '' && is_array($val.data_value) && isset($val.data_value[$vo.label])"}

							<option value="{$vo.label}" {eq name="$val.data_value[$vo.label]" value="$vo.label" }selected{/eq}>{$vo.label}</option>

					{else/}
						<option value="{$vo.label}" {eq name="$row[$val['id']]" value="$vo.label" }selected{/eq}>{$vo.label}</option>
					{/if}
				{else/}
				<option value="{$vo.label}" >{$vo.label}</option>
				{/if}
			{/foreach}

		</select>
		{/if}
	</div>
</div>
{/eq}

{eq name="val.component" value="TimePicker"}
<div class="col-xs-12 col-sm-6">
	<div class="form-group col-sm-10">
		<label for="c-{$val.id}">{$val.config.label}:</label>

		<input id="c-{$val.id}" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss"
			   placeholder="{$val.config.placeholder}"{if condition="$val.config.required == true"}data-rule="required"{/if} name="row[{$val.id}]" {if condition="isset($row[$val['id']])"}value="{:$row[$val['id']]}"{/if} type="text">
	</div>
</div>
{/eq}

{eq name="val.component" value="DatePicker"}
<div class="col-xs-12 col-sm-6">
	<div class="form-group col-sm-10">
		<label for="c-{$val.id}">{$val.config.label}:</label>

		<input id="c-{$val.id}" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" placeholder="{$val.config.placeholder}"
			   name="row[{$val.id}]" type="text" {if condition="$val.config.required == true"}data-rule="required"{/if}
		{if condition="isset($row[$val['id']])"}value="{:$row[$val['id']]}"{/if} >
	</div>
</div>
{/eq}
{eq name="val.component" value="Rate"}
<div class="col-xs-12 col-sm-6">
	<div class="form-group col-sm-10">
		<label for="c-{$val.id}">{$val.config.label}:</label>
		<select id="c-{$val.id}" name="row[{$val.id}]" class="form-control">
			{if condition="isset($row[$val['id']])"}
			<option value="5" {eq name="$row[$val['id']]" value="5" }selected{/eq} >5</option>
			<option value="4" {eq name="$row[$val['id']]" value="4" }selected{/eq} >4</option>
			<option value="3" {eq name="$row[$val['id']]" value="3" }selected{/eq} >3</option>
			<option value="2" {eq name="$row[$val['id']]" value="2" }selected{/eq} >2</option>
			<option value="1" {eq name="$row[$val['id']]" value="1" }selected{/eq} >1</option>
			{else/}
			<option value="5">5</option>
			<option value="4">4</option>
			<option value="3">3</option>
			<option value="2">2</option>
			<option value="1">1</option>
			{/if}
		</select>
	</div>
</div>
{/eq}
{eq name="val.component" value="uploadImage"}
<div class="col-xs-12 col-sm-6">
	<div class="form-group col-sm-10">
		<label for="c-{$val.id}">{$val.config.label}:</label>
		<div class="input-group">
			<input id="p-{$val.id}" class="form-control" size="35" name="row[{$val.id}]" type="text"  {if condition="$val.config.required == true"}data-rule="required"{/if} {if condition="isset($row[$val['id'].'_str'])"}value="{:$row[$val['id'].'_str']}"{/if}>
			<div class="input-group-addon no-border no-padding">
						<span><button type="button" class="btn btn-danger faupload"
									  data-resize-quality="0.8"
									  data-resize-width="1024"
									  data-resize-height="768"
									  data-input-id="p-{$val.id}"
									  data-url="qingdong/base/upload"
									  data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp"
									  data-multiple="true" data-preview-id="c-{$val.id}"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>

			</div>
			<span class="msg-box n-right"></span>
		</div>
		<ul class="row list-inline faupload-preview" id="c-{$val.id}"></ul>
	</div>
</div>
{/eq}

{eq name="val.component" value="uploadFile"}
<div class="col-xs-12 col-sm-6">
	<div class="form-group col-sm-10">
		<label for="c-{$val.id}">{$val.config.label}:</label>
		<div class="input-group">
			<input id="p-{$val.id}" class="form-control" size="35" name="row[{$val.id}]" type="text"  {if condition="$val.config.required == true"}data-rule="required"{/if} {if condition="isset($row[$val['id'].'_str'])"}value="{:$row[$val['id'].'_str']}"{/if}>
			<div class="input-group-addon no-border no-padding">
						<span><button type="button" class="btn btn-danger faupload"
									  data-input-id="p-{$val.id}"
									  data-url="qingdong/base/upload"
									  data-multiple="true" data-preview-id="c-{$val.id}"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>

			</div>
			<span class="msg-box n-right"></span>
		</div>
	</div>
</div>
{/eq}
{/if}
{/foreach}