<?php

namespace app\admin\controller\qingdong\contract;

use app\admin\controller\qingdong\Base;
use addons\qingdong\model\ContractTemplate;
use addons\qingdong\model\Product;
use addons\qingdong\model\Staff;
use think\Db;
use think\Exception;

/**
 * 合同模板管理
 */
class Template extends Base
{
    protected $model = null;
    protected $searchFields = 'id,name';

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new ContractTemplate;
    }

    /**
     * 查看模板列表
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            
            $list = $this->model->where($where)
                ->with(['createStaff'])
                ->order($sort, $order)->paginate($limit);
            
            $result = array("total" => $list->total(), "rows" => $list->items());
            return json($result);
        }
        
        return $this->view->fetch();
    }

    /**
     * 新增模板
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            
            try {
                // 验证必要字段
                if (empty($params['name'])) {
                    $this->error('模板名称不能为空');
                }
                
                if (empty($params['content'])) {
                    $this->error('模板内容不能为空');
                }
                
                // 检查名称重复
                if ($this->model->where('name', $params['name'])->find()) {
                    $this->error('模板名称已存在');
                }
                
                $template = ContractTemplate::createTemplate($params);
                $this->success('模板创建成功');
                
            } catch (Exception $e) {
                $this->error($e->getMessage());
            }
        }
        
        // 获取产品列表用于关联
        $products = Product::where('status', 1)
            ->field('id,name,price')
            ->limit(100)->select();
        
        $this->assign('products', $products);
        return $this->view->fetch();
    }

    /**
     * 编辑模板
     */
    public function edit($ids = null)
    {
        $row = $this->model->find($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            
            try {
                // 检查名称重复
                if (isset($params['name']) && $params['name'] != $row->name) {
                    if ($this->model->where('name', $params['name'])->find()) {
                        $this->error('模板名称已存在');
                    }
                }
                
                $result = $row->allowField(true)->save($params);
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            } catch (Exception $e) {
                $this->error($e->getMessage());
            }
        }
        
        // 解析占位符
        if ($row['placeholders']) {
            $row['placeholders_array'] = json_decode($row['placeholders'], true);
        }
        
        $products = Product::where('status', 1)
            ->field('id,name,price')
            ->limit(100)->select();
        
        $this->assign('row', $row);
        $this->assign('products', $products);
        return $this->view->fetch();
    }

    /**
     * 删除模板
     */
    public function del($ids = "")
    {
        if (!$this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        
        $ids = $ids ? $ids : $this->request->post("ids");
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }
        
        if (!is_array($ids)) {
            $ids = explode(',', $ids);
        }
        
        $count = 0;
        Db::startTrans();
        try {
            foreach ($ids as $id) {
                $row = $this->model->find($id);
                if ($row) {
                    // 检查是否有合同在使用该模板
                    $contractCount = \addons\qingdong\model\Contract::where('template_id', $id)->count();
                    if ($contractCount > 0) {
                        $this->error('模板【' . $row->name . '】正在被 ' . $contractCount . ' 个合同使用，无法删除');
                    }
                    
                    $count += $row->delete();
                }
            }
            Db::commit();
            
            if ($count) {
                $this->success();
            } else {
                $this->error(__('No rows were deleted'));
            }
        } catch (Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
    }

    /**
     * 预览模板
     */
    public function preview($ids = null)
    {
        $template = $this->model->find($ids);
        if (!$template) {
            $this->error('模板不存在');
        }
        
        // 模拟数据用于预览
        $mockData = [
            'customer_name' => '北京科技有限公司',
            'contact_name' => '张三',
            'contact_mobile' => '13800138000',
            'money' => 50000.00,
            'staff_name' => '李销售',
            'products' => [
                ['name' => '产品A', 'number' => 2, 'price' => 15000.00],
                ['name' => '产品B', 'number' => 1, 'price' => 20000.00]
            ]
        ];
        
        $content = $template->applyTemplate($mockData);
        
        $this->assign('template', $template);
        $this->assign('content', $content);
        return $this->view->fetch();
    }

    /**
     * 复制模板
     */
    public function copy($ids = null)
    {
        $template = $this->model->find($ids);
        if (!$template) {
            $this->error('模板不存在');
        }
        
        try {
            $data = $template->toArray();
            unset($data['id']);
            $data['name'] = $data['name'] . '_副本_' . date('md');
            $data['create_staff_id'] = $this->_staff->id;
            $data['createtime'] = time();
            
            $newTemplate = new ContractTemplate;
            $result = $newTemplate->allowField(true)->save($data);
            
            if ($result) {
                $this->success('模板复制成功', url('index'));
            } else {
                $this->error('复制失败');
            }
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
    }
} 